class Delivery {
  final String id;
  final String productName;
  final int qty;
  final double productPrice;
  final double deliveryFee;
  final String receiverPhone;
  final String receiverName;
  final String receiverAddress;
  final String deliveryCode;
  final String deliveryStatus;
  final String paymentStatus;
  final String dueDate;
  final List<int> uploadDate;

  Delivery({
    required this.id,
    required this.productName,
    required this.qty,
    required this.productPrice,
    required this.deliveryFee,
    required this.receiverPhone,
    required this.receiverName,
    required this.receiverAddress,
    required this.deliveryCode,
    required this.deliveryStatus,
    required this.paymentStatus,
    required this.dueDate,
    required this.uploadDate,
  });

  factory Delivery.fromJson(Map<String, dynamic> json) {
    return Delivery(
      id: json['id']?.toString() ?? '',
      productName: json['productName']?.toString() ?? '',
      qty: json['qty'] ?? 0,
      productPrice: (json['productPrice'] ?? 0).toDouble(),
      deliveryFee: (json['deliveryFee'] ?? 0).toDouble(),
      receiverPhone: json['receiverPhone']?.toString() ?? '',
      receiverName: json['receiverName']?.toString() ?? '',
      receiverAddress: json['receiverAddress']?.toString() ?? '',
      deliveryCode: json['deliveryCode']?.toString() ?? '',
      deliveryStatus: json['deliveryStatus']?.toString() ?? '',
      paymentStatus: json['paymentStatus']?.toString() ?? '',
      dueDate: json['dueDate']?.toString() ?? '',
      uploadDate: json['uploadDate'] != null 
          ? List<int>.from(json['uploadDate'].map((x) => x is int ? x : int.tryParse(x.toString()) ?? 0))
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'productName': productName,
      'qty': qty,
      'productPrice': productPrice,
      'deliveryFee': deliveryFee,
      'receiverPhone': receiverPhone,
      'receiverName': receiverName,
      'receiverAddress': receiverAddress,
      'deliveryCode': deliveryCode,
      'deliveryStatus': deliveryStatus,
      'paymentStatus': paymentStatus,
      'dueDate': dueDate,
      'uploadDate': uploadDate,
    };
  }

  @override
  String toString() {
    return 'Delivery(id: $id, productName: $productName, deliveryCode: $deliveryCode, deliveryStatus: $deliveryStatus, receiverAddress: $receiverAddress)';
  }
}
