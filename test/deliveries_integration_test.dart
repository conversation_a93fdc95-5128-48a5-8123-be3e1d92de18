import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:mialamobile/models/delivery.dart';
import 'package:mialamobile/api/endpoints.dart';
import 'package:mialamobile/utils/delivery_utils.dart';

void main() {
  group('Deliveries Integration Tests', () {
    test('Delivery model should parse JSON correctly', () {
      // Test data based on the expected API response
      final testData = {
        'id': '123',
        'productName': 'iPhone 15 Pro Max',
        'qty': 2,
        'productPrice': 500000.0,
        'deliveryFee': 5000.0,
        'receiverPhone': '+2348012345678',
        'receiverName': '<PERSON>',
        'receiverAddress': '123 Main Street, Lagos',
        'deliveryCode': 'MLA-34-D6D976',
        'deliveryStatus': 'PENDING',
        'paymentStatus': 'NOT_PAID',
        'dueDate': '2024-03-15',
        'uploadDate': [2024, 3, 10],
      };

      final delivery = Delivery.fromJson(testData);

      expect(delivery.id, equals('123'));
      expect(delivery.productName, equals('iPhone 15 Pro Max'));
      expect(delivery.qty, equals(2));
      expect(delivery.productPrice, equals(500000.0));
      expect(delivery.deliveryFee, equals(5000.0));
      expect(delivery.receiverPhone, equals('+2348012345678'));
      expect(delivery.receiverName, equals('John Doe'));
      expect(delivery.receiverAddress, equals('123 Main Street, Lagos'));
      expect(delivery.deliveryCode, equals('MLA-34-D6D976'));
      expect(delivery.deliveryStatus, equals('PENDING'));
      expect(delivery.paymentStatus, equals('NOT_PAID'));
      expect(delivery.dueDate, equals('2024-03-15'));
      expect(delivery.uploadDate, equals([2024, 3, 10]));
    });

    test('Delivery model should handle missing fields with defaults', () {
      // Test data with missing fields
      final testData = <String, dynamic>{};

      final delivery = Delivery.fromJson(testData);

      expect(delivery.id, equals(''));
      expect(delivery.productName, equals(''));
      expect(delivery.qty, equals(0));
      expect(delivery.productPrice, equals(0.0));
      expect(delivery.deliveryFee, equals(0.0));
      expect(delivery.receiverPhone, equals(''));
      expect(delivery.receiverName, equals(''));
      expect(delivery.receiverAddress, equals(''));
      expect(delivery.deliveryCode, equals(''));
      expect(delivery.deliveryStatus, equals(''));
      expect(delivery.paymentStatus, equals(''));
      expect(delivery.dueDate, equals(''));
      expect(delivery.uploadDate, equals([]));
    });

    test('DeliveryUtils should format dates correctly', () {
      expect(DeliveryUtils.formatDeliveryDate('2024-03-15'), equals('15Mar'));
      expect(DeliveryUtils.formatDeliveryDate('2024-12-25'), equals('25Dec'));
      expect(DeliveryUtils.formatDeliveryDate('invalid-date'), equals('invalid-date'));
    });

    test('DeliveryUtils should format delivery time correctly', () {
      final time1 = DeliveryUtils.formatDeliveryTime([2024, 3, 15]);
      expect(time1, isNotEmpty);
      expect(time1, contains(RegExp(r'\d{1,2}:\d{2}(am|pm)')));

      final time2 = DeliveryUtils.formatDeliveryTime([]);
      expect(time2, equals('12:00pm'));
    });

    test('DeliveryUtils should return correct status colors', () {
      expect(DeliveryUtils.getStatusColor('DELIVERED'), equals(const Color(0xff153D80)));
      expect(DeliveryUtils.getStatusColor('PENDING'), equals(const Color(0xffFFA500)));
      expect(DeliveryUtils.getStatusColor('FAILED'), equals(const Color(0xffB10303)));
      expect(DeliveryUtils.getStatusColor('ASSIGNED'), equals(const Color(0xff4CAF50)));
      expect(DeliveryUtils.getStatusColor('UNKNOWN'), equals(const Color(0xff8C8C8C)));
    });

    test('DeliveryUtils should format delivery status correctly', () {
      expect(DeliveryUtils.formatDeliveryStatus('PENDING'), equals('Pending'));
      expect(DeliveryUtils.formatDeliveryStatus('IN_TRANSIT'), equals('In Transit'));
      expect(DeliveryUtils.formatDeliveryStatus('NOT_PAID'), equals('Not Paid'));
      expect(DeliveryUtils.formatDeliveryStatus('delivered'), equals('Delivered'));
    });

    test('DeliveryUtils should limit deliveries correctly', () {
      final deliveries = List.generate(10, (index) => 'delivery_$index');
      
      final limited = DeliveryUtils.limitToRecent(deliveries, limit: 5);
      expect(limited.length, equals(5));
      expect(limited, equals(['delivery_0', 'delivery_1', 'delivery_2', 'delivery_3', 'delivery_4']));

      final smallList = ['delivery_1', 'delivery_2'];
      final limitedSmall = DeliveryUtils.limitToRecent(smallList, limit: 5);
      expect(limitedSmall.length, equals(2));
      expect(limitedSmall, equals(smallList));
    });

    test('API endpoint should be correctly defined', () {
      expect(ApiEndpoints.deliveries, isNotEmpty);
      expect(ApiEndpoints.deliveries, contains('/rider/deliveries'));
    });

    test('Delivery model should convert to JSON correctly', () {
      final delivery = Delivery(
        id: '123',
        productName: 'Test Product',
        qty: 1,
        productPrice: 1000.0,
        deliveryFee: 500.0,
        receiverPhone: '+2348012345678',
        receiverName: 'Test User',
        receiverAddress: 'Test Address',
        deliveryCode: 'TEST-123',
        deliveryStatus: 'PENDING',
        paymentStatus: 'NOT_PAID',
        dueDate: '2024-03-15',
        uploadDate: [2024, 3, 10],
      );

      final json = delivery.toJson();

      expect(json['id'], equals('123'));
      expect(json['productName'], equals('Test Product'));
      expect(json['qty'], equals(1));
      expect(json['deliveryCode'], equals('TEST-123'));
      expect(json['deliveryStatus'], equals('PENDING'));
    });
  });
}
